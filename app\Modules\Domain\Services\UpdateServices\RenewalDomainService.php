<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Events\UpdateDomainsTableEvent;
use App\Modules\AccountCredit\Services\AccountCreditService;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\JobPayloadKeys;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\JobServices\JobDispatchService;
use App\Modules\Epp\Constants\RegistryTransactionType;
use App\Modules\Epp\Services\RegistryAccountBalanceService;
use App\Modules\Payment\Services\PaymentFeeService;
use App\Modules\Payment\Services\PaymentInvoiceService;
use App\Modules\Payment\Services\PaymentReimbursementService;
use App\Modules\PaymentMethod\Services\PaymentMethodService;
use App\Modules\PaymentSummary\Constants\PaymentSummaryType;
use App\Modules\PaymentSummary\Services\PaymentSummaryService;
use App\Modules\Setting\Constants\FeeType;
use App\Modules\Setting\Constants\SettingKey;
use App\Modules\Setting\Services\DomainFees;
use App\Modules\Setting\Services\ExtensionFees;
use App\Modules\Setting\Services\Settings;
use App\Modules\Stripe\Helpers\StripeFeeHelper;
use App\Modules\Stripe\Providers\PaymentIntentProvider;
use App\Modules\Stripe\Services\StripeLimiter;
use App\Util\Helper\Domain\DomainParser;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

class RenewalDomainService
{
    use UserLoggerTrait;

    private $other_fees;

    private $isJob = true;

    private $userEmail;

    private ?int $consoleUserId = null; // Store user ID for console commands

    private $dispatchThirtySeconds = 30;

    private $dispatchDelayInSeconds = 180; // three minutes

    public static function instance(): self
    {
        $renewalDomainService = new self;

        return $renewalDomainService;
    }

    public function setConsoleUserId(int $userId): self
    {
        $this->consoleUserId = $userId;
        return $this;
    }

    public function getData(array $request): array
    {
        StripeLimiter::instance()->checkAttempt();

        $domains = $request['domains'];
        $renewalFees = ExtensionFees::instance()->getDefaultFeesbyType(FeeType::RENEW);
        $penalties = DomainFees::instance()->getPenalties();
        $redemptionFee = DomainFees::instance()->getFee(FeeType::REDEMPTION);
        $gracePeriod = intval(Settings::instance()->getValueByKey(SettingKey::DOMAIN_GRACE_PERIOD));
        $settings = [
            'renewal_fees' => $renewalFees,
            'penalties' => $penalties,
            'redemption_fee' => $redemptionFee,
            'gracePeriod' => $gracePeriod
        ];

        return [
            'domains' => $domains,
            'settings' => $settings,
        ];
    }

    public function getRenewalPaymentData(array $request): array
    {
        $domains = $request['domains'];
        $other_fees = PaymentFeeService::getOtherRenewalFees($domains);
        $stripeFeeObj = StripeFeeHelper::calculateTransactionFee($other_fees['bill_total'] ?? 0);
        $payment = PaymentIntentProvider::instance()->createPaymentDetails($stripeFeeObj['gross_amount'] ?? $other_fees['bill_total']);
        $setupIntents = PaymentIntentProvider::instance()->create($payment);
        $accountCredit = AccountCreditService::instance()->getLatestBlock($this->getUserId());

        // For auto-renewal, we want to be less strict with registry balance checks
        // Allow the process to continue even if registry balance is low, as domain renewal is critical
        $result = RegistryAccountBalanceService::checkRegistryBalance($domains, $other_fees, FeeType::RENEW);

        $primaryPaymentMethod= (new PaymentMethodService())->fetchPrimaryPaymentMethod($this->getUserId());
        $default_card = (new PaymentMethodService())->fetchDefaultCard($this->getUserId());

        $data = [
            'domains' => $domains,
            'other_fees' => $other_fees,
            'secret' => $setupIntents->client_secret,
            'intent' => $setupIntents->id,
            'promise' => Config::get('stripe.publishable_key'),
            'account_credit_balance' => $accountCredit->running_balance ?? 0,
            'stripeFeeObj' => $stripeFeeObj,
            'primary_payment_method' => $primaryPaymentMethod,
            'default_card' => $default_card
        ];

        // Only add balance error if it's critical - for auto-renewal we want to be more permissive
        if ($result['error'] && strpos(strtolower($result['message'] ?? ''), 'insufficient') !== false) {
            app(\App\Modules\CustomLogger\Services\AuthLogger::class)->info("RenewalDomainService: Registry balance warning for auto-renewal: " . $result['message']);
            // Don't add balance_error to allow auto-renewal to proceed
        }

        return $data;
    }

    public function update(array $request): string
    {
        // Store user ID for console commands (auto-renewal)
        if (isset($request['user_id'])) {
            $this->consoleUserId = $request['user_id'];
        }

        // Only clear Stripe rate limiting for Stripe payments
        if ($request['payment_service_type'] === \App\Modules\PaymentService\Constants\PaymentServiceType::STRIPE) {
            StripeLimiter::instance()->clearAttempt();
        }

        $paymentPayload = $this->createPaymentPayload($request);
        $invoiceId = PaymentSummaryService::instance()->createPayment($paymentPayload, $this->getUserId(), $request['payment_summary_type'], $request['payment_service_type']);
        $refundDetails = PaymentReimbursementService::instance()->createRefundDetails(PaymentSummaryType::PAYMENT_REIMBURSEMENT, $request['payment_service_type'], $invoiceId);

        $this->renewDomainJob($request['domains'], $refundDetails);
        ScheduleDomainExpiryNotice::dispatch($this->getUserId())->delay($this->dispatchDelayInSeconds);

        return $invoiceId;
    }

    public function checkAndCreditRegistryBalance(array $request)
    {
        $registryId_balance = $this->checkRegistryBalance($request, FeeType::RENEW);
        $this->creditRegistryAccountBalance($registryId_balance);
    }

    // PRIVATE FUNCTIONS

    private function checkRegistryBalance(array $data, string $type): array
    {
        $result = RegistryAccountBalanceService::checkRegistryBalance($data['domains'], $data['other_fees'], $type, $data['intent'] ?? '');
        if ($result['error']) {
            throw new \Exception($result['message']);
        }
        return $result['registryBalance'];
    }

    private function creditRegistryAccountBalance(array $registryBalance): void
    {
        foreach ($registryBalance as $balance) {
            RegistryAccountBalanceService::credit($balance['balance'], $balance['amount'], RegistryTransactionType::SUB_FUND, RegistryTransactionType::DOMAIN_RENEWAL);
        }
    }

    private function setDomainsToProcess(Collection $domains)
    {
        $domainRenewIds = $domains->pluck('id')->toArray();
        DomainService::instance()->updateDomainStatus($domainRenewIds, DomainStatus::IN_PROCESS, true);
        UpdateDomainsTableEvent::dispatch($this->getUserId());
    }

    private function getDefaultPayload()
    {
        return [
            JobPayloadKeys::USER_ID => $this->getUserId(),
            JobPayloadKeys::EMAIL => $this->getUserEmail(),
        ];
    }

    private function getJobPayload(object $domain, array $default, array $refundData)
    {
        $registry = DomainParser::getRegistryName($domain->name);
        $registeredDomain = [
            'id' => $domain->registered_domain_id,
            'name' => $domain->name,
        ];

        return [
            JobPayloadKeys::DOMAIN => $domain,
            JobPayloadKeys::REGISTERED_DOMAIN => json_decode(json_encode($registeredDomain), false),
            JobPayloadKeys::USER_ID => $default[JobPayloadKeys::USER_ID],
            JobPayloadKeys::REGISTRY => $registry,
            JobPayloadKeys::EMAIL => $default[JobPayloadKeys::EMAIL],
            JobPayloadKeys::UPDATE_TYPE => \App\Modules\Domain\Constants\DomainJobTypes::UPDATE_RENEWAL,
            JobPayloadKeys::REFUND_DATA => $refundData,

        ];
    }

    private function renewDomainJob(array $domains, array $refundData)
    {
        // This function triggers the renewal process for the given domains.
        $domainObj = collect(json_decode(json_encode($domains), false));
        $this->setDomainsToProcess($domainObj);
        $defaultPayload = $this->getDefaultPayload();

        $count = 0;
        foreach ($domainObj as $domain) {
            JobDispatchService::instance()->renewEppDispatch(
                $this->getJobPayload($domain, $defaultPayload, $refundData),
                $this->dispatchThirtySeconds + $count
            );
            $count += 1;
        }
    }

    private function getUserId(): int
    {
        // Use console user ID if set (for auto-renewal commands)
        if ($this->consoleUserId !== null) {
            return $this->consoleUserId;
        }

        return Auth::user()->id ?? 0;
    }

    private function getUserEmail(): string
    {
        // Use console user ID if set (for auto-renewal commands)
        if ($this->consoleUserId !== null) {
            $user = \App\Models\User::find($this->consoleUserId);
            return $user ? $user->email : 'AutoRenewal@system';
        }

        return Auth::user()->email ?? 'Unauthorized';
    }

    private function createPaymentPayload(array $request)
    {
        $otherFees = json_decode(json_encode($request['other_fees']), true);
        $registeredDomains = collect(json_decode(json_encode($request['domains']), false));

        $invoice = PaymentInvoiceService::instance()->createInvoicePayload(FeeType::RENEW, $this->getUserId(), $otherFees, $request['intent'] ?? null);
        $nodeInvoice = PaymentInvoiceService::instance()->createNodeInvoicePayload(FeeType::RENEW, $registeredDomains, $otherFees);

        return PaymentInvoiceService::instance()->createPaymentPayload($invoice, $nodeInvoice);
    }
}
