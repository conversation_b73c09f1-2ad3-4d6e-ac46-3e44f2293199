import { useState, useRef } from "react";
import DropDownContainer from "@/Components/DropDownContainer";
import Checkbox from "@/Components/Checkbox";
import SecondaryButton from "@/Components/SecondaryButton";
import { MdDelete, MdOutlineUpdate, MdWarning } from "react-icons/md";
import { getEventValue } from "@/Util/TargetInputEvent";
import useOutsideClick from "@/Util/useOutsideClick";
import InputError from "@/Components/InputError";
import setDefaultDateFormat from "../../../Util/setDefaultDateFormat";

export default function RedemptionItems({
    index,
    domain,
    handleChangeItem,
    onDeleteItem,
    renewalPrice,
    redemptionFee
}) {
    const [yearToggle, setYearToggle] = useState(false);
    const ref = useRef();
    const MAX_YEARS = 10;
    const YEAR_BY_SECS = 31557600;

    useOutsideClick(ref, () => setYearToggle(false));

    const calculateMaxExpiry = (selectedExpiry) => {
        var todayEpoch = Date.parse(new Date());
        var createdEpoch = Date.parse(domain.created_at);
        var addToCreatedYears = parseInt(new Date(domain.created_at).getFullYear()) + parseInt(MAX_YEARS);
        var expiredNumYears = Math.floor(((todayEpoch - createdEpoch) / 1000.0 / YEAR_BY_SECS));
        var addToMaxExpiryYear = addToCreatedYears + expiredNumYears;
        var createdMaxTermEpoch = new Date(domain.created_at).setFullYear(addToMaxExpiryYear);
        var selectedExpiryEpoch = Date.parse(selectedExpiry);
        var isMax = (selectedExpiryEpoch >= createdMaxTermEpoch) ? true : false;

        return isMax;
    }

    const getRestoreDate = (year) => {
        const domainExpiry = new Date(domain.expiry);

        var calculatedExpiry = new Date(domainExpiry.getFullYear() + year, domainExpiry.getMonth(), domainExpiry.getDate());
        return calculatedExpiry;
    }

    const [expiry, setExpiry] = useState(getRestoreDate(domain.year_length))

    // for checkboxes
    const onHandleChange = (event) => {
        domain[event.target.name] = getEventValue(event);
        handleChangeItem(index, domain, event.target.name, getEventValue(event));
    };

    // for dropdown
    const onHandleChangeYear = (year) => {
        setExpiry(getRestoreDate(year));
        domain.year_length = year;
        setYearToggle(!yearToggle)
        handleChangeItem(index, domain, "year_length", year, calculateMaxExpiry(getRestoreDate(year)));
    };

    const priceFormat = (year) => {
        const renewalTotal = year * renewalPrice;
        const totalPrice = renewalTotal + redemptionFee;
        return parseFloat(totalPrice).toFixed(2);
    }

    const yearList = [...Array(10).keys()].map((i) => (
        <button
            key={"yearlist" + i}
            className="hover:bg-gray-100 px-5 py-1"
            onClick={() => onHandleChangeYear(i + 1)}
        >
            {i + 1} {i > 0 ? " years" : "  year"}
        </button>
    ));

    return (
        <div className={`" space-y-2" }`}>
            <div className="flex items-center justify-between text-gray-600 border-b border-gray-200 pb-2 mb-4">
                <span className="flex items-center space-x-2">
                    <label className="text-2xl">{domain.name}</label>
                    {/* {penalty > 0 && <MdWarning size={22} className="text-danger" />} */}
                </span>
                <button onClick={() => onDeleteItem(domain.id, index)}>
                    <MdDelete className=" text-3xl hover:bg-black hover:bg-opacity-20  rounded-full p-1 transition duration-150 cursor-pointer " />
                </button>
            </div>
            {/* Redemption fee breakdown */}
            <div ref={ref} className="flex items-center mb-1 justify-between hover:bg-gray-50 relative">
                <div className="flex items-center">
                    <MdWarning size={19} className="text-blue-600" />
                    <span className="ml-2 text-md text-blue-600">
                        Renewal fee: ${(renewalPrice * domain.year_length).toFixed(2)} + Redemption fee: ${redemptionFee.toFixed(2)}
                    </span>
                </div>
            </div>
            <div ref={ref} className="flex items-center justify-between hover:bg-gray-50 relative">
                <div className="flex items-center">
                    <MdOutlineUpdate className=" text-lg" />
                    <span className="ml-2 text-md text-gray-600">
                        Restore until {setDefaultDateFormat(expiry)}
                    </span>
                </div>
                <SecondaryButton name="show_year" onClick={() => setYearToggle(!yearToggle)}>
                    <span>
                        ${priceFormat(domain.year_length)} / {domain.year_length}{" "}
                        {domain.year_length > 1 ? "years" : "year"}
                    </span>
                    <svg
                        className="ml-2 -mr-0.5 h-4 w-4"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                    >
                        <path
                            fillRule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clipRule="evenodd"
                        />
                    </svg>
                </SecondaryButton>
                <DropDownContainer show={yearToggle} className="right-0 -bottom-[3rem] bg-white top-10 h-36 overflow-y-scroll">
                    {yearList}
                </DropDownContainer>
            </div>
            {calculateMaxExpiry(expiry) && (<div
                className="flex items-center justify-between hover:bg-gray-50 relative"
            >
                <div className="flex items-center">
                    <InputError
                        message="Maximum registration period exceeded. Please remove domain or lower the year value selected."
                        className="ml-2 text-md w-full"
                    />
                </div>
            </div>)}
        </div>
    );
}